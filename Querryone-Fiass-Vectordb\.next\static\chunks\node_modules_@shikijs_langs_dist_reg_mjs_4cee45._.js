(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/node_modules_@shikijs_langs_dist_reg_mjs_4cee45._.js", {

"[project]/node_modules/@shikijs/langs/dist/reg.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
const lang = Object.freeze(JSON.parse("{\"displayName\":\"Windows Registry Script\",\"fileTypes\":[\"reg\",\"REG\"],\"name\":\"reg\",\"patterns\":[{\"match\":\"Windows Registry Editor Version 5\\\\.00|REGEDIT4\",\"name\":\"keyword.control.import.reg\"},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.comment.reg\"}},\"match\":\"(;).*$\",\"name\":\"comment.line.semicolon.reg\"},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.section.reg\"},\"2\":{\"name\":\"entity.section.reg\"},\"3\":{\"name\":\"punctuation.definition.section.reg\"}},\"match\":\"^\\\\s*(\\\\[(?!-))(.*?)(\\\\])\",\"name\":\"entity.name.function.section.add.reg\"},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.section.reg\"},\"2\":{\"name\":\"entity.section.reg\"},\"3\":{\"name\":\"punctuation.definition.section.reg\"}},\"match\":\"^\\\\s*(\\\\[-)(.*?)(\\\\])\",\"name\":\"entity.name.function.section.delete.reg\"},{\"captures\":{\"2\":{\"name\":\"punctuation.definition.quote.reg\"},\"3\":{\"name\":\"support.function.regname.ini\"},\"4\":{\"name\":\"punctuation.definition.quote.reg\"},\"5\":{\"name\":\"punctuation.definition.equals.reg\"},\"7\":{\"name\":\"keyword.operator.arithmetic.minus.reg\"},\"9\":{\"name\":\"punctuation.definition.quote.reg\"},\"10\":{\"name\":\"string.name.regdata.reg\"},\"11\":{\"name\":\"punctuation.definition.quote.reg\"},\"13\":{\"name\":\"support.type.dword.reg\"},\"14\":{\"name\":\"keyword.operator.arithmetic.colon.reg\"},\"15\":{\"name\":\"constant.numeric.dword.reg\"},\"17\":{\"name\":\"support.type.dword.reg\"},\"18\":{\"name\":\"keyword.operator.arithmetic.parenthesis.reg\"},\"19\":{\"name\":\"keyword.operator.arithmetic.parenthesis.reg\"},\"20\":{\"name\":\"constant.numeric.hex.size.reg\"},\"21\":{\"name\":\"keyword.operator.arithmetic.parenthesis.reg\"},\"22\":{\"name\":\"keyword.operator.arithmetic.colon.reg\"},\"23\":{\"name\":\"constant.numeric.hex.reg\"},\"24\":{\"name\":\"keyword.operator.arithmetic.linecontinuation.reg\"},\"25\":{\"name\":\"comment.declarationline.semicolon.reg\"}},\"match\":\"^(\\\\s*([\\\"']?)(.+?)([\\\"']?)\\\\s*(=))?\\\\s*((-)|(([\\\"'])(.*?)([\\\"']))|(((?i:dword))(\\\\:)\\\\s*([\\\\dabcdefABCDEF]{1,8}))|(((?i:hex))((\\\\()([\\\\d]*)(\\\\)))?(\\\\:)(.*?)(\\\\\\\\?)))\\\\s*(;.*)?$\",\"name\":\"meta.declaration.reg\"},{\"match\":\"[0-9]+\",\"name\":\"constant.numeric.reg\"},{\"match\":\"[a-fA-F]+\",\"name\":\"constant.numeric.hex.reg\"},{\"match\":\",+\",\"name\":\"constant.numeric.hex.comma.reg\"},{\"match\":\"\\\\\\\\\",\"name\":\"keyword.operator.arithmetic.linecontinuation.reg\"}],\"scopeName\":\"source.reg\"}"));
const __TURBOPACK__default__export__ = [
    lang
];
}}),
}]);

//# sourceMappingURL=node_modules_%40shikijs_langs_dist_reg_mjs_4cee45._.js.map