{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/node_modules/%40shikijs/langs/dist/jssm.mjs"], "sourcesContent": ["const lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"JSSM\\\",\\\"fileTypes\\\":[\\\"jssm\\\",\\\"jssm_state\\\"],\\\"name\\\":\\\"jssm\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.mn\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.jssm\\\"},{\\\"begin\\\":\\\"//\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"comment.line.jssm\\\"},{\\\"begin\\\":\\\"\\\\\\\\${\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"entity.name.function\\\"}},\\\"end\\\":\\\"}\\\",\\\"name\\\":\\\"keyword.other\\\"},{\\\"match\\\":\\\"([0-9]*)(\\\\\\\\.)([0-9]*)(\\\\\\\\.)([0-9]*)\\\",\\\"name\\\":\\\"constant.numeric\\\"},{\\\"match\\\":\\\"graph_layout(\\\\\\\\s*)(:)\\\",\\\"name\\\":\\\"constant.language.jssmLanguage\\\"},{\\\"match\\\":\\\"machine_name(\\\\\\\\s*)(:)\\\",\\\"name\\\":\\\"constant.language.jssmLanguage\\\"},{\\\"match\\\":\\\"machine_version(\\\\\\\\s*)(:)\\\",\\\"name\\\":\\\"constant.language.jssmLanguage\\\"},{\\\"match\\\":\\\"jssm_version(\\\\\\\\s*)(:)\\\",\\\"name\\\":\\\"constant.language.jssmLanguage\\\"},{\\\"match\\\":\\\"<->\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.legal_legal\\\"},{\\\"match\\\":\\\"<-\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.legal_none\\\"},{\\\"match\\\":\\\"->\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.none_legal\\\"},{\\\"match\\\":\\\"<=>\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.main_main\\\"},{\\\"match\\\":\\\"=>\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.none_main\\\"},{\\\"match\\\":\\\"<=\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.main_none\\\"},{\\\"match\\\":\\\"<~>\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.forced_forced\\\"},{\\\"match\\\":\\\"~>\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.none_forced\\\"},{\\\"match\\\":\\\"<~\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.forced_none\\\"},{\\\"match\\\":\\\"<-=>\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.legal_main\\\"},{\\\"match\\\":\\\"<=->\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.main_legal\\\"},{\\\"match\\\":\\\"<-~>\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.legal_forced\\\"},{\\\"match\\\":\\\"<~->\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.forced_legal\\\"},{\\\"match\\\":\\\"<=~>\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.main_forced\\\"},{\\\"match\\\":\\\"<~=>\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.forced_main\\\"},{\\\"match\\\":\\\"([0-9]+)%\\\",\\\"name\\\":\\\"constant.numeric.jssmProbability\\\"},{\\\"match\\\":\\\"\\\\\\\\'[^']*\\\\\\\\'\\\",\\\"name\\\":\\\"constant.character.jssmAction\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\"[^\\\\\\\"]*\\\\\\\\\\\\\\\"\\\",\\\"name\\\":\\\"entity.name.tag.jssmLabel.doublequoted\\\"},{\\\"match\\\":\\\"([a-zA-Z0-9_.+&()#@!?,])\\\",\\\"name\\\":\\\"entity.name.tag.jssmLabel.atom\\\"}],\\\"scopeName\\\":\\\"source.jssm\\\",\\\"aliases\\\":[\\\"fsl\\\"]}\"))\n\nexport default [\nlang\n]\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC;uCAEvB;IACf;CACC", "ignoreList": [0]}}, {"offset": {"line": 14, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}