(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/node_modules_@shikijs_langs_dist_desktop_mjs_6fb320._.js", {

"[project]/node_modules/@shikijs/langs/dist/desktop.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
const lang = Object.freeze(JSON.parse("{\"displayName\":\"Desktop\",\"name\":\"desktop\",\"patterns\":[{\"include\":\"#layout\"},{\"include\":\"#keywords\"},{\"include\":\"#values\"},{\"include\":\"#inCommands\"},{\"include\":\"#inCategories\"}],\"repository\":{\"inCategories\":{\"patterns\":[{\"match\":\"(?<=^Categories.*)AudioVideo|(?<=^Categories.*)Audio|(?<=^Categories.*)Video|(?<=^Categories.*)Development|(?<=^Categories.*)Education|(?<=^Categories.*)Game|(?<=^Categories.*)Graphics|(?<=^Categories.*)Network|(?<=^Categories.*)Office|(?<=^Categories.*)Science|(?<=^Categories.*)Settings|(?<=^Categories.*)System|(?<=^Categories.*)Utility\",\"name\":\"markup.bold\"}]},\"inCommands\":{\"patterns\":[{\"match\":\"(?<=^Exec.*\\\\s)-+\\\\S+\",\"name\":\"variable.parameter\"},{\"match\":\"(?<=^Exec.*)\\\\s\\\\%[fFuUick]\\\\s\",\"name\":\"variable.language\"},{\"match\":\"\\\".*\\\"\",\"name\":\"string\"}]},\"keywords\":{\"patterns\":[{\"match\":\"^Type\\\\b|^Version\\\\b|^Name\\\\b|^GenericName\\\\b|^NoDisplay\\\\b|^Comment\\\\b|^Icon\\\\b|^Hidden\\\\b|^OnlyShowIn\\\\b|^NotShowIn\\\\b|^DBusActivatable\\\\b|^TryExec\\\\b|^Exec\\\\b|^Path\\\\b|^Terminal\\\\b|^Actions\\\\b|^MimeType\\\\b|^Categories\\\\b|^Implements\\\\b|^Keywords\\\\b|^StartupNotify\\\\b|^StartupWMClass\\\\b|^URL\\\\b|^PrefersNonDefaultGPU\\\\b|^Encoding\\\\b\",\"name\":\"keyword\"},{\"match\":\"^X-[A-z 0-9 -]*\",\"name\":\"keyword.other\"},{\"match\":\"(?<!^)\\\\[.+\\\\]\",\"name\":\"constant.language\"},{\"match\":\"^GtkTheme\\\\b|^MetacityTheme\\\\b|^IconTheme\\\\b|^CursorTheme\\\\b|^ButtonLayout\\\\b|^ApplicationFont\\\\b\",\"name\":\"keyword\"}]},\"layout\":{\"patterns\":[{\"begin\":\"^\\\\[Desktop\",\"end\":\"\\\\]\",\"name\":\"markup.heading\"},{\"begin\":\"^\\\\[X-\\\\w*\",\"end\":\"\\\\]\",\"name\":\"markup.heading\"},{\"match\":\"^\\\\s*#.*\",\"name\":\"comment\"},{\"match\":\";\",\"name\":\"strong\"}]},\"values\":{\"patterns\":[{\"match\":\"(?<=^\\\\S+)=\",\"name\":\"keyword.operator\"},{\"match\":\"\\\\btrue\\\\b|\\\\bfalse\\\\b\",\"name\":\"variable.other\"},{\"match\":\"(?<=^Version.*)\\\\d+(\\\\.{0,1}\\\\d*)\",\"name\":\"variable.other\"}]}},\"scopeName\":\"source.desktop\"}"));
const __TURBOPACK__default__export__ = [
    lang
];
}}),
}]);

//# sourceMappingURL=node_modules_%40shikijs_langs_dist_desktop_mjs_6fb320._.js.map